<template>
  <div class="login">
    <LoginLeftView></LoginLeftView>

    <div class="right-wrap">
      <div class="header">
        <ArtLogo class="icon" />
        <h1>{{ systemName }}</h1>
      </div>
      <div class="login-wrap">
        <div class="form">
          <h3 class="title">{{ $t('login.title') }}</h3>
          <p class="sub-title">{{ $t('login.subTitle') }}</p>
          <ElForm
            ref="formRef"
            :model="formData"
            :rules="rules"
            @keyup.enter="handleSubmit"
            style="margin-top: 25px"
          >
            <!-- <ElFormItem prop="account">
              <ElSelect v-model="formData.account" @change="setupAccount" class="account-select">
                <ElOption
                  v-for="account in accounts"
                  :key="account.key"
                  :label="account.label"
                  :value="account.key"
                >
                  <span>{{ account.label }}</span>
                </ElOption>
              </ElSelect>
            </ElFormItem> -->
            <ElFormItem prop="email">
              <ElInput :placeholder="'请输入邮箱地址'" v-model.trim="formData.email" type="email" />
            </ElFormItem>
            <ElFormItem prop="password">
              <ElInput
                :placeholder="$t('login.placeholder[1]')"
                v-model.trim="formData.password"
                type="password"
                radius="8px"
                autocomplete="off"
                show-password
              />
            </ElFormItem>

            <!-- 验证码组件 -->
            <ElFormItem prop="captcha">
              <ArtCaptchaInput
                ref="captchaRef"
                v-model="formData.captcha"
                placeholder="请输入验证码"
                @captcha-change="handleCaptchaChange"
                @enter="handleSubmit"
              />
            </ElFormItem>
            <!-- <div class="drag-verify">
              <div class="drag-verify-content" :class="{ error: !isPassing && isClickPass }">
                <ArtDragVerify
                  ref="dragVerify"
                  v-model:value="isPassing"
                  :text="$t('login.sliderText')"
                  textColor="var(--art-gray-800)"
                  :successText="$t('login.sliderSuccessText')"
                  :progressBarBg="getCssVar('--el-color-primary')"
                  background="var(--art-gray-200)"
                  handlerBg="var(--art-main-bg-color)"
                />
              </div>
              <p class="error-text" :class="{ 'show-error-text': !isPassing && isClickPass }">{{
                $t('login.placeholder[2]')
              }}</p>
            </div> -->

            <!-- <div class="forget-password">
              <ElCheckbox v-model="formData.rememberPassword">{{
                $t('login.rememberPwd')
              }}</ElCheckbox>
              <RouterLink :to="RoutesAlias.ForgetPassword">{{ $t('login.forgetPwd') }}</RouterLink>
            </div> -->

            <div style="margin-top: 30px">
              <ElButton
                class="login-btn"
                type="primary"
                @click="handleSubmit"
                :loading="loading"
                v-ripple
              >
                {{ $t('login.btnText') }}
              </ElButton>
            </div>

            <!-- <div class="footer">
              <p>
                {{ $t('login.noAccount') }}
                <RouterLink :to="RoutesAlias.Register">{{ $t('login.register') }}</RouterLink>
              </p>
            </div> -->
          </ElForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import AppConfig from '@/config'
  import { ElNotification, ElMessage } from 'element-plus'
  import { useUserStore } from '@/store/modules/user'
  import { useI18n } from 'vue-i18n'
  import { HttpError } from '@/utils/http/error'
  import { UserService } from '@/api/usersApi'
  import type { FormInstance, FormRules } from 'element-plus'

  defineOptions({ name: 'Login' })

  const { t } = useI18n()

  type AccountKey = 'super' | 'admin' | 'user'

  export interface Account {
    key: AccountKey
    label: string
    userName: string
    password: string
    roles: string[]
  }

  // const accounts = computed<Account[]>(() => [
  //   {
  //     key: 'super',
  //     label: t('login.roles.super'),
  //     userName: 'Super',
  //     password: '123456',
  //     roles: ['R_SUPER']
  //   },
  //   {
  //     key: 'admin',
  //     label: t('login.roles.admin'),
  //     userName: 'Admin',
  //     password: '123456',
  //     roles: ['R_ADMIN']
  //   },
  //   {
  //     key: 'user',
  //     label: t('login.roles.user'),
  //     userName: 'User',
  //     password: '123456',
  //     roles: ['R_USER']
  //   }
  // ])

  const captchaRef = ref()

  const userStore = useUserStore()
  const router = useRouter()

  // 验证码相关状态
  const captchaKey = ref('')

  const systemName = AppConfig.systemInfo.name
  const formRef = ref<FormInstance>()

  const formData = reactive({
    account: '',
    email: '',
    password: '',
    captcha: '',
    rememberPassword: true
  })

  const rules = computed<FormRules>(() => ({
    email: [
      { required: true, message: '请输入邮箱地址', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: ['blur', 'change'] }
    ],
    password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
    captcha: [
      { required: true, message: '请输入验证码', trigger: 'blur' },
      { pattern: /^[a-zA-Z0-9]{4}$/, message: '验证码格式错误，请输入4位字母或数字' }
    ]
  }))

  const loading = ref(false)

  onMounted(() => {
    // setupAccount('super')
  })

  // 设置账号
  // const setupAccount = (key: AccountKey) => {
  //   const selectedAccount = accounts.value.find((account: Account) => account.key === key)
  //   formData.account = key
  //   formData.username = selectedAccount?.userName ?? ''
  //   formData.password = selectedAccount?.password ?? ''
  // }

  /**
   * 处理验证码变化
   * @param _captcha 验证码值
   * @param key 验证码密钥
   */
  const handleCaptchaChange = (_captcha: string, key: string) => {
    captchaKey.value = key
  }

  // 登录
  const handleSubmit = async () => {
    if (!formRef.value) return
    const valid = await formRef.value.validate()
    if (!valid) return
    try {
      // 表单验证
    

      loading.value = true

      // 登录请求
      const { email, password, captcha } = formData

      const res = await UserService.login({
        email,
        password,
        key: captchaKey.value,
        code: captcha
      })

      // 验证token
      const { token } = res
      if (!token) {
        throw new Error('Login failed - no token received')
      }

      // 存储token和用户信息
      userStore.setToken(token, token)
      // const userInfo = await UserService.getUserInfo()
      const userInfo = {
          userId: 1,
          userName: 'zhuxiangcheng',
          roles: ['R_SUPER'],
          buttons: [],
          avatar: '',
          email: '<EMAIL>',
          phone: ''
          }
      userStore.setUserInfo(userInfo)
      userStore.setLoginStatus(true)

      // 登录成功处理
      showLoginSuccessNotice()
      router.push('/')
    } catch (error) {
      // 处理 HttpError
      if (error instanceof HttpError) {
        // console.log(error.code)
        // 刷新验证码
        if (captchaRef.value) {
          captchaRef.value.refreshCaptcha()
        }
      } else {
        // 处理非 HttpError
        ElMessage.error('登录失败，请稍后重试')
        console.error('[Login] Unexpected error:', error)
      
      }
    } finally {
      loading.value = false
    }
  }

  // 登录成功提示
  const showLoginSuccessNotice = () => {
    setTimeout(() => {
      ElNotification({
        title: t('login.success.title'),
        type: 'success',
        duration: 2500,
        zIndex: 10000,
        message: `${t('login.success.message')}, ${systemName}!`
      })
    }, 150)
  }
</script>

<style lang="scss" scoped>
  @use './index';
</style>
