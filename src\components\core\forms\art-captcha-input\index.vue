<!-- 验证码输入组件 -->
<template>
  <div class="captcha-container">
    <div class="captcha-input-section">
      <el-input
        v-model="captchaValue"
        :placeholder="placeholder"
        class="captcha-input"
        maxlength="4"
        @input="handleChange"
        @keyup.enter="$emit('enter')"
      />
    </div>
    <div class="captcha-image-section">
      <div class="captcha-image-wrapper" @click="refreshCaptcha">
        <img v-if="qrCodeUrl" :src="qrCodeUrl" alt="验证码" class="captcha-image" />
        <div v-else class="captcha-loading">
          <el-icon class="is-loading">
            <Loading />
          </el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch } from 'vue'
  import { Loading } from '@element-plus/icons-vue'
  import { getCaptcha } from '@/api/captchaApi'

  defineOptions({ name: 'ArtCaptchaInput' })

  /**
   * 组件Props接口定义
   */
  interface CaptchaInputProps {
    /** 占位符文本 */
    placeholder?: string
    /** 验证码值 */
    modelValue?: string
  }

  /**
   * 组件事件接口定义
   */
  interface CaptchaInputEmits {
    /** 验证码值更新事件 */
    'update:modelValue': [value: string]
    /** 验证码变化事件，包含验证码值和密钥 */
    'captcha-change': [captcha: string, captchaKey: string]
    /** 回车键事件 */
    enter: []
  }

  const props = withDefaults(defineProps<CaptchaInputProps>(), {
    placeholder: '请输入验证码',
    modelValue: ''
  })

  const emit = defineEmits<CaptchaInputEmits>()

  // 响应式数据
  const captchaValue = ref('')
  const qrCodeUrl = ref('')
  const captchaKey = ref('')

  /**
   * 获取验证码
   * 从服务器获取新的验证码图片和密钥
   */
  const fetchCaptcha = async () => {
    try {
      qrCodeUrl.value = '' // 清空图片，显示加载状态
      const response = await getCaptcha()

      // 设置验证码图片和密钥
      qrCodeUrl.value = 'data:image/jpeg;base64,' + response.img_code
      captchaKey.value = response.key

      // 清空当前输入的验证码
      captchaValue.value = ''
      emit('update:modelValue', '')
      emit('captcha-change', '', captchaKey.value)

      console.log('验证码获取成功')
    } catch (error) {
      console.error('获取验证码失败:', error)
    }
  }

  /**
   * 刷新验证码
   * 点击验证码图片时触发
   */
  const refreshCaptcha = () => {
    fetchCaptcha()
  }

  /**
   * 处理验证码输入变化
   * @param value 输入的验证码值
   */
  const handleChange = (value: string) => {
    captchaValue.value = value
    emit('update:modelValue', value)
    emit('captcha-change', value, captchaKey.value)
  }

  // 监听外部传入的值变化
  watch(
    () => props.modelValue,
    (newValue) => {
      if (newValue !== captchaValue.value) {
        captchaValue.value = newValue
      }
    },
    { immediate: true }
  )

  // 暴露刷新验证码方法给父组件
  defineExpose({
    refreshCaptcha,
    getCaptchaKey: () => captchaKey.value
  })

  // 组件挂载时获取验证码
  onMounted(() => {
    fetchCaptcha()
  })
</script>

<style lang="scss" scoped>
  .captcha-container {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;

    .captcha-input-section {
      flex: 1;
      min-width: 0; // 防止flex项目溢出

      .captcha-input {
        width: 100%;
        height: 40px;

        :deep(.el-input__inner) {
          height: 40px;
          line-height: 40px;
          font-size: 16px;
          letter-spacing: 2px;
          text-align: center;
        }
      }
    }

    .captcha-image-section {
      flex-shrink: 0;
      width: 120px; // 固定宽度确保图片有足够空间

      .captcha-image-wrapper {
        width: 100%;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid var(--el-border-color);
        border-radius: var(--el-border-radius-base);
        cursor: pointer;
        transition: all 0.3s ease;
        background-color: var(--el-bg-color);
        overflow: hidden; // 确保图片不会溢出容器

        &:hover {
          border-color: var(--el-color-primary);
          box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
        }

        .captcha-image {
          width: 100%;
          height: 100%;
          object-fit: cover; // 改为cover确保图片填满容器
          user-select: none;
          display: block;
        }

        .captcha-loading {
          color: var(--el-text-color-placeholder);
          font-size: 20px;
        }
      }
    }
  }

  // 移动端适配
  @media (max-width: 768px) {
    .captcha-container {
      gap: 8px;

      .captcha-image-section {
        width: 100px; // 移动端稍微缩小图片区域

        .captcha-image-wrapper {
          height: 36px; // 移动端稍微降低高度
        }
      }

      .captcha-input-section {
        .captcha-input {
          height: 36px;

          :deep(.el-input__inner) {
            height: 36px;
            line-height: 36px;
            font-size: 14px;
          }
        }
      }
    }
  }
</style>
