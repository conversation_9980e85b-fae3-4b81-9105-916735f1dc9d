/**
 * 验证码相关API接口
 */
import request from '@/utils/http'

/**
 * 验证码响应数据接口
 */
export interface CaptchaResponse {
  /** 验证码图片的base64编码 */
  img_code: string
  /** 验证码密钥，用于后续验证 */
  key: string
}

/**
 * 获取验证码
 * @returns Promise<CaptchaResponse> 验证码响应数据
 */
export const getCaptcha = (): Promise<CaptchaResponse> => {
  return request.post<CaptchaResponse>({
    url: '/captcha'
  })
}

/**
 * 验证验证码（可选接口，如果后端需要单独验证）
 * @param captcha 用户输入的验证码
 * @param key 验证码密钥
 * @returns Promise<boolean> 验证结果
 */
export const verifyCaptcha = (captcha: string, key: string): Promise<boolean> => {
  return request.post<boolean>({
    url: '/captcha/verify',
    data: {
      captcha,
      key
    }
  })
}
